class Venue {
  final String id;
  final String name;
  final String description;
  final String location;
  final String mainImage;
  final List<String> images;
  final double basePrice;
  final int capacity;
  final List<String> includedItems;
  final List<VenuePackage> packages;
  final double rating;
  final int reviewCount;
  final String contactNumber;
  final String email;
  final Map<String, String> amenities;

  Venue({
    required this.id,
    required this.name,
    required this.description,
    required this.location,
    required this.mainImage,
    required this.images,
    required this.basePrice,
    required this.capacity,
    required this.includedItems,
    required this.packages,
    required this.rating,
    required this.reviewCount,
    required this.contactNumber,
    required this.email,
    required this.amenities,
  });
}

class VenuePackage {
  final String id;
  final String name;
  final String description;
  final double price;
  final int guestCount;
  final List<String> includedServices;
  final List<String> foodItems;
  final String duration;
  final bool isPopular;

  VenuePackage({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.guestCount,
    required this.includedServices,
    required this.foodItems,
    required this.duration,
    this.isPopular = false,
  });
}

// Sample data for venues
class VenueData {
  static List<Venue> getSampleVenues() {
    return [
      Venue(
        id: '1',
        name: 'Royal Palace Banquet Hall',
        description: 'Elegant and spacious banquet hall perfect for weddings and special occasions. Features traditional architecture with modern amenities.',
        location: 'Gulshan-e-Iqbal, Karachi',
        mainImage: 'https://images.unsplash.com/photo-1519167758481-83f550bb49b3?w=800',
        images: [
          'https://images.unsplash.com/photo-1519167758481-83f550bb49b3?w=800',
          'https://images.unsplash.com/photo-1464366400600-7168b8af9bc3?w=800',
          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800',
        ],
        basePrice: 150000,
        capacity: 500,
        includedItems: [
          'Air Conditioning',
          'Sound System',
          'Lighting Setup',
          'Parking Space',
          'Security',
          'Decoration Setup',
        ],
        packages: [
          VenuePackage(
            id: 'p1',
            name: 'Silver Package',
            description: 'Basic wedding package with essential services',
            price: 200000,
            guestCount: 300,
            includedServices: [
              'Venue Booking',
              'Basic Decoration',
              'Sound System',
              'Photography (2 hours)',
            ],
            foodItems: [
              'Chicken Karahi',
              'Mutton Biryani',
              'Raita',
              'Salad',
              'Dessert',
            ],
            duration: '6 hours',
          ),
          VenuePackage(
            id: 'p2',
            name: 'Gold Package',
            description: 'Premium wedding package with enhanced services',
            price: 350000,
            guestCount: 400,
            includedServices: [
              'Venue Booking',
              'Premium Decoration',
              'Sound & Light System',
              'Photography & Videography',
              'Mehndi Setup',
            ],
            foodItems: [
              'Chicken Karahi',
              'Mutton Biryani',
              'Fish Curry',
              'Raita',
              'Salad',
              'Dessert',
              'Welcome Drinks',
            ],
            duration: '8 hours',
            isPopular: true,
          ),
          VenuePackage(
            id: 'p3',
            name: 'Platinum Package',
            description: 'Luxury wedding package with all premium services',
            price: 500000,
            guestCount: 500,
            includedServices: [
              'Venue Booking',
              'Luxury Decoration',
              'Professional Sound & Light',
              'Full Photography & Videography',
              'Mehndi & Baraat Setup',
              'Bridal Room',
            ],
            foodItems: [
              'Chicken Karahi',
              'Mutton Biryani',
              'Fish Curry',
              'BBQ Items',
              'Raita',
              'Salad',
              'Multiple Desserts',
              'Welcome Drinks',
              'Live Cooking Station',
            ],
            duration: '12 hours',
          ),
        ],
        rating: 4.5,
        reviewCount: 128,
        contactNumber: '+92-300-1234567',
        email: '<EMAIL>',
        amenities: {
          'Parking': '200 cars',
          'AC Halls': '3 halls',
          'Bridal Room': 'Available',
          'Kitchen': 'Full equipped',
          'Generator': 'Backup power',
        },
      ),
      Venue(
        id: '2',
        name: 'Grand Marquee',
        description: 'Beautiful outdoor marquee setup with garden view. Perfect for traditional weddings with a touch of nature.',
        location: 'DHA Phase 5, Karachi',
        mainImage: 'https://images.unsplash.com/photo-1511795409834-ef04bbd61622?w=800',
        images: [
          'https://images.unsplash.com/photo-1511795409834-ef04bbd61622?w=800',
          'https://images.unsplash.com/photo-1505236858219-8359eb29e329?w=800',
          'https://images.unsplash.com/photo-1464366400600-7168b8af9bc3?w=800',
        ],
        basePrice: 120000,
        capacity: 400,
        includedItems: [
          'Marquee Setup',
          'Garden Area',
          'Sound System',
          'Lighting',
          'Parking',
          'Security',
        ],
        packages: [
          VenuePackage(
            id: 'p4',
            name: 'Garden Package',
            description: 'Outdoor wedding package with garden setup',
            price: 180000,
            guestCount: 250,
            includedServices: [
              'Marquee Setup',
              'Garden Decoration',
              'Sound System',
              'Basic Photography',
            ],
            foodItems: [
              'Chicken Karahi',
              'Biryani',
              'Raita',
              'Salad',
            ],
            duration: '6 hours',
          ),
          VenuePackage(
            id: 'p5',
            name: 'Premium Garden',
            description: 'Enhanced outdoor wedding with premium services',
            price: 280000,
            guestCount: 350,
            includedServices: [
              'Premium Marquee',
              'Floral Decoration',
              'Professional Sound & Light',
              'Photography & Videography',
            ],
            foodItems: [
              'Chicken Karahi',
              'Mutton Biryani',
              'BBQ Items',
              'Raita',
              'Salad',
              'Dessert',
            ],
            duration: '8 hours',
            isPopular: true,
          ),
        ],
        rating: 4.3,
        reviewCount: 89,
        contactNumber: '+92-300-9876543',
        email: '<EMAIL>',
        amenities: {
          'Parking': '150 cars',
          'Garden Area': '2000 sq ft',
          'Marquee': 'Weather proof',
          'Kitchen': 'Available',
          'Washrooms': '6 units',
        },
      ),
    ];
  }
}
