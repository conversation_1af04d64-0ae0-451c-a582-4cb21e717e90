class CateringService {
  final String id;
  final String name;
  final String description;
  final String image;
  final List<CateringPackage> packages;
  final List<MenuItem> menuItems;
  final double rating;
  final int reviewCount;
  final String contactNumber;
  final String email;

  CateringService({
    required this.id,
    required this.name,
    required this.description,
    required this.image,
    required this.packages,
    required this.menuItems,
    required this.rating,
    required this.reviewCount,
    required this.contactNumber,
    required this.email,
  });
}

class CateringPackage {
  final String id;
  final String name;
  final String description;
  final double pricePerPerson;
  final int minimumPersons;
  final List<String> includedItems;
  final bool isPopular;
  final String category; // e.g., "Basic", "Premium", "Luxury"

  CateringPackage({
    required this.id,
    required this.name,
    required this.description,
    required this.pricePerPerson,
    required this.minimumPersons,
    required this.includedItems,
    this.isPopular = false,
    required this.category,
  });
}

class MenuItem {
  final String id;
  final String name;
  final String description;
  final double pricePerPerson;
  final String category; // e.g., "Main Course", "Appetizer", "Dessert"
  final String image;
  final bool isVegetarian;
  final bool isSpicy;

  MenuItem({
    required this.id,
    required this.name,
    required this.description,
    required this.pricePerPerson,
    required this.category,
    required this.image,
    this.isVegetarian = false,
    this.isSpicy = false,
  });
}

// Sample data for catering services
class CateringData {
  static List<CateringService> getSampleCateringServices() {
    return [
      CateringService(
        id: '1',
        name: 'Royal Catering Services',
        description:
            'Premium catering services specializing in traditional Pakistani cuisine for weddings and special events.',
        image:
            'https://images.unsplash.com/photo-1555939594-58d7cb561ad1?w=800',
        rating: 4.6,
        reviewCount: 156,
        contactNumber: '+92-300-1111111',
        email: '<EMAIL>',
        packages: [
          CateringPackage(
            id: 'cp1',
            name: 'Basic Wedding Package',
            description: 'Essential catering package for intimate weddings',
            pricePerPerson: 800,
            minimumPersons: 100,
            category: 'Basic',
            includedItems: [
              'Chicken Karahi',
              'Mutton Biryani',
              'Raita',
              'Salad',
              'Naan',
              'Dessert',
              'Soft Drinks',
            ],
          ),
          CateringPackage(
            id: 'cp2',
            name: 'Premium Wedding Package',
            description:
                'Enhanced catering with more variety and premium dishes',
            pricePerPerson: 1200,
            minimumPersons: 150,
            category: 'Premium',
            isPopular: true,
            includedItems: [
              'Chicken Karahi',
              'Mutton Biryani',
              'Fish Curry',
              'BBQ Items',
              'Raita',
              'Salad',
              'Naan & Roti',
              'Multiple Desserts',
              'Welcome Drinks',
              'Tea/Coffee',
            ],
          ),
          CateringPackage(
            id: 'cp3',
            name: 'Luxury Wedding Package',
            description:
                'Complete luxury catering experience with live cooking stations',
            pricePerPerson: 1800,
            minimumPersons: 200,
            category: 'Luxury',
            includedItems: [
              'Multiple Chicken Dishes',
              'Mutton Biryani & Pulao',
              'Fish & Seafood',
              'BBQ Counter',
              'Live Cooking Station',
              'Appetizers',
              'Raita & Salads',
              'Fresh Bread Station',
              'Premium Desserts',
              'Welcome Drinks',
              'Tea/Coffee Service',
              'Fruit Counter',
            ],
          ),
        ],
        menuItems: [
          MenuItem(
            id: 'm1',
            name: 'Chicken Karahi',
            description: 'Traditional spicy chicken curry cooked in karahi',
            pricePerPerson: 150,
            category: 'Main Course',
            image:
                'https://images.unsplash.com/photo-1603894584373-5ac82b2ae398?w=400',
            isSpicy: true,
          ),
          MenuItem(
            id: 'm2',
            name: 'Mutton Biryani',
            description: 'Aromatic basmati rice with tender mutton pieces',
            pricePerPerson: 200,
            category: 'Main Course',
            image:
                'https://images.unsplash.com/photo-1563379091339-03246963d96c?w=400',
            isSpicy: true,
          ),
          MenuItem(
            id: 'm3',
            name: 'Fish Curry',
            description: 'Fresh fish cooked in traditional spices',
            pricePerPerson: 180,
            category: 'Main Course',
            image:
                'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=400',
            isSpicy: true,
          ),
          MenuItem(
            id: 'm4',
            name: 'BBQ Platter',
            description:
                'Assorted grilled items including chicken tikka, seekh kebab',
            pricePerPerson: 220,
            category: 'BBQ',
            image:
                'https://images.unsplash.com/photo-1544025162-d76694265947?w=400',
            isSpicy: true,
          ),
          MenuItem(
            id: 'm5',
            name: 'Vegetable Biryani',
            description: 'Fragrant rice with mixed vegetables and spices',
            pricePerPerson: 120,
            category: 'Main Course',
            image:
                'https://images.unsplash.com/photo-1596797038530-2c107229654b?w=400',
            isVegetarian: true,
          ),
          MenuItem(
            id: 'm6',
            name: 'Raita',
            description: 'Cool yogurt-based side dish with cucumber',
            pricePerPerson: 30,
            category: 'Side Dish',
            image:
                'https://images.unsplash.com/photo-1606491956689-2ea866880c84?w=400',
            isVegetarian: true,
          ),
          MenuItem(
            id: 'm7',
            name: 'Mixed Salad',
            description: 'Fresh seasonal vegetables and greens',
            pricePerPerson: 25,
            category: 'Side Dish',
            image:
                'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400',
            isVegetarian: true,
          ),
          MenuItem(
            id: 'm8',
            name: 'Gulab Jamun',
            description: 'Traditional sweet dumplings in sugar syrup',
            pricePerPerson: 40,
            category: 'Dessert',
            image:
                'https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=400',
            isVegetarian: true,
          ),
          MenuItem(
            id: 'm9',
            name: 'Kheer',
            description: 'Creamy rice pudding with nuts and cardamom',
            pricePerPerson: 35,
            category: 'Dessert',
            image:
                'https://images.unsplash.com/photo-1571115764595-644a1f56a55c?w=400',
            isVegetarian: true,
          ),
        ],
      ),
    ];
  }
}
