import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/catering.dart';
import '../theme/app_theme.dart';

class CateringCustomizationScreen extends StatefulWidget {
  final CateringService cateringService;
  final CateringPackage? selectedPackage;
  final int personCount;

  const CateringCustomizationScreen({
    super.key,
    required this.cateringService,
    this.selectedPackage,
    required this.personCount,
  });

  @override
  State<CateringCustomizationScreen> createState() => _CateringCustomizationScreenState();
}

class _CateringCustomizationScreenState extends State<CateringCustomizationScreen> {
  late int currentPersonCount;
  Map<String, bool> selectedMenuItems = {};
  Map<String, List<MenuItem>> categorizedMenuItems = {};

  @override
  void initState() {
    super.initState();
    currentPersonCount = widget.personCount;
    
    // Categorize menu items
    for (var item in widget.cateringService.menuItems) {
      if (!categorizedMenuItems.containsKey(item.category)) {
        categorizedMenuItems[item.category] = [];
      }
      categorizedMenuItems[item.category]!.add(item);
      
      // Pre-select items if package is selected
      if (widget.selectedPackage != null) {
        selectedMenuItems[item.id] = widget.selectedPackage!.includedItems
            .any((includedItem) => includedItem.toLowerCase().contains(item.name.toLowerCase()));
      } else {
        selectedMenuItems[item.id] = false;
      }
    }
  }

  double get totalPrice {
    double total = 0;
    for (var item in widget.cateringService.menuItems) {
      if (selectedMenuItems[item.id] == true) {
        total += item.pricePerPerson * currentPersonCount;
      }
    }
    return total;
  }

  int get selectedItemsCount {
    return selectedMenuItems.values.where((selected) => selected).length;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.primaryDark,
      appBar: AppBar(
        title: Text(
          widget.selectedPackage?.name ?? 'Custom Menu',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: AppTheme.primaryGold,
          ),
        ),
        backgroundColor: AppTheme.backgroundDark,
        iconTheme: const IconThemeData(color: AppTheme.textLight),
      ),
      body: Column(
        children: [
          // Header with person count and total
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: AppTheme.cardBackground,
              boxShadow: [
                BoxShadow(
                  color: Colors.black26,
                  blurRadius: 4,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                // Person Count Selector
                Row(
                  children: [
                    Text(
                      'Guests: ',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: AppTheme.textLight,
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        if (currentPersonCount > 50) {
                          setState(() {
                            currentPersonCount -= 10;
                          });
                        }
                      },
                      icon: const Icon(Icons.remove_circle_outline),
                      color: AppTheme.primaryGold,
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: AppTheme.surface,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        '$currentPersonCount',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: AppTheme.primaryGold,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        if (currentPersonCount < 1000) {
                          setState(() {
                            currentPersonCount += 10;
                          });
                        }
                      },
                      icon: const Icon(Icons.add_circle_outline),
                      color: AppTheme.primaryGold,
                    ),
                    const Spacer(),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '$selectedItemsCount items selected',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppTheme.textSecondary,
                          ),
                        ),
                        Text(
                          'PKR ${totalPrice.toStringAsFixed(0)}',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            color: AppTheme.primaryGold,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Menu Items List
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: categorizedMenuItems.keys.length,
              itemBuilder: (context, index) {
                final category = categorizedMenuItems.keys.elementAt(index);
                final items = categorizedMenuItems[category]!;
                
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Category Header
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      child: Text(
                        category,
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          color: AppTheme.primaryGold,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    
                    // Category Items
                    ...items.map((item) => CustomizableMenuItemCard(
                      menuItem: item,
                      personCount: currentPersonCount,
                      isSelected: selectedMenuItems[item.id] ?? false,
                      onSelectionChanged: (selected) {
                        setState(() {
                          selectedMenuItems[item.id] = selected;
                        });
                      },
                    )),
                    
                    const SizedBox(height: 16),
                  ],
                );
              },
            ),
          ),
        ],
      ),
      
      // Bottom Action Bar
      bottomNavigationBar: Container(
        padding: const EdgeInsets.all(16),
        decoration: const BoxDecoration(
          color: AppTheme.cardBackground,
          boxShadow: [
            BoxShadow(
              color: Colors.black26,
              blurRadius: 4,
              offset: Offset(0, -2),
            ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Total for $currentPersonCount guests',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                  ),
                  Text(
                    'PKR ${totalPrice.toStringAsFixed(0)}',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: AppTheme.primaryGold,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            ElevatedButton(
              onPressed: selectedItemsCount > 0 ? () {
                _showOrderSummary(context);
              } : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryGold,
                foregroundColor: AppTheme.primaryDark,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
              child: const Text('Place Order'),
            ),
          ],
        ),
      ),
    );
  }

  void _showOrderSummary(BuildContext context) {
    final selectedItems = widget.cateringService.menuItems
        .where((item) => selectedMenuItems[item.id] == true)
        .toList();

    showModalBottomSheet(
      context: context,
      backgroundColor: AppTheme.cardBackground,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Order Summary',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: AppTheme.primaryGold,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Text(
              'Guests: $currentPersonCount',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: AppTheme.textLight,
              ),
            ),
            const SizedBox(height: 8),
            
            Text(
              'Selected Items:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppTheme.textLight,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            
            ...selectedItems.map((item) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    item.name,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppTheme.textLight,
                    ),
                  ),
                  Text(
                    'PKR ${(item.pricePerPerson * currentPersonCount).toStringAsFixed(0)}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            )),
            
            const Divider(color: AppTheme.textSecondary),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Total:',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: AppTheme.textLight,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'PKR ${totalPrice.toStringAsFixed(0)}',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: AppTheme.primaryGold,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _showContactDialog(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryGold,
                  foregroundColor: AppTheme.primaryDark,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('Confirm Order'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showContactDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.cardBackground,
        title: Text(
          'Contact for Booking',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: AppTheme.primaryGold,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Please contact us to finalize your order:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textLight,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(Icons.phone, color: AppTheme.primaryGold),
                const SizedBox(width: 8),
                Text(
                  widget.cateringService.contactNumber,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textLight,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.email, color: AppTheme.primaryGold),
                const SizedBox(width: 8),
                Text(
                  widget.cateringService.email,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textLight,
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

class CustomizableMenuItemCard extends StatelessWidget {
  final MenuItem menuItem;
  final int personCount;
  final bool isSelected;
  final ValueChanged<bool> onSelectionChanged;

  const CustomizableMenuItemCard({
    super.key,
    required this.menuItem,
    required this.personCount,
    required this.isSelected,
    required this.onSelectionChanged,
  });

  @override
  Widget build(BuildContext context) {
    final totalPrice = menuItem.pricePerPerson * personCount;

    return Card(
      elevation: 2,
      color: isSelected ? AppTheme.primaryGold.withOpacity(0.1) : AppTheme.cardBackground,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isSelected 
          ? const BorderSide(color: AppTheme.primaryGold, width: 2)
          : BorderSide.none,
      ),
      child: InkWell(
        onTap: () => onSelectionChanged(!isSelected),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // Image
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: CachedNetworkImage(
                  imageUrl: menuItem.image,
                  width: 60,
                  height: 60,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    width: 60,
                    height: 60,
                    color: AppTheme.surface,
                    child: const Center(
                      child: CircularProgressIndicator(
                        color: AppTheme.primaryGold,
                        strokeWidth: 2,
                      ),
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    width: 60,
                    height: 60,
                    color: AppTheme.surface,
                    child: const Icon(
                      Icons.image_not_supported,
                      color: AppTheme.textSecondary,
                    ),
                  ),
                ),
              ),
              
              const SizedBox(width: 12),
              
              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            menuItem.name,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: AppTheme.textLight,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        if (menuItem.isVegetarian)
                          const Icon(
                            Icons.eco,
                            color: Colors.green,
                            size: 16,
                          ),
                        if (menuItem.isSpicy)
                          const Icon(
                            Icons.local_fire_department,
                            color: Colors.red,
                            size: 16,
                          ),
                      ],
                    ),
                    
                    const SizedBox(height: 4),
                    
                    Text(
                      menuItem.description,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.textSecondary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const SizedBox(height: 8),
                    
                    Text(
                      'PKR ${totalPrice.toStringAsFixed(0)}',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        color: AppTheme.primaryGold,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Checkbox
              Checkbox(
                value: isSelected,
                onChanged: (value) => onSelectionChanged(value ?? false),
                activeColor: AppTheme.primaryGold,
                checkColor: AppTheme.primaryDark,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
