import 'package:flutter/material.dart';
import 'theme/app_theme.dart';
import 'screens/home_screen.dart';

void main() {
  runApp(const MezbaanApp());
}

class MezbaanApp extends StatelessWidget {
  const MezbaanApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '<PERSON><PERSON><PERSON> <PERSON> <PERSON>, <PERSON>ahan <PERSON>',
      theme: AppTheme.darkTheme,
      home: const HomeScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}
